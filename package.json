{"name": "kanban-task-bord", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.11", "@types/react-redux": "^7.1.34", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "tailwindcss": "^4.1.11", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}