import BoardPage from '@/pages/BoardPage';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import type { AppDispatch, RootState } from './redux';
import { setTheme } from './redux/slice/themeSlice';
import { STORAGE_KEYS } from './constants';

/**
 * ThemeInitializer Component
 *
 * This component handles theme initialization and management for the entire application.
 * It performs the following functions:
 * 1. Listens to system theme changes and updates the Redux state accordingly
 * 2. Applies the current theme to the document element by adding/removing the 'dark' class
 * 3. Persists theme changes to localStorage for consistency across sessions
 *
 * The component acts as a theme provider replacement, ensuring that theme changes
 * are properly reflected in both the application state and the DOM.
 */
function ThemeInitializer({ children }: { children: React.ReactNode }) {
  const dispatch = useDispatch<AppDispatch>();
  const theme = useSelector((state: RootState) => state.theme);

  // Effect to handle system theme changes
  useEffect(() => {
    // Listen for system theme preference changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      // Only update if no theme is saved in localStorage (user hasn't manually set a preference)
      const savedTheme = localStorage.getItem(STORAGE_KEYS.THEME);
      if (!savedTheme) {
        dispatch(setTheme(e.matches ? 'dark' : 'light'));
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);

    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, [dispatch]);

  // Effect to apply theme changes to the DOM and persist to localStorage
  useEffect(() => {
    // Apply theme to document element for Tailwind CSS dark mode
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Persist theme preference to localStorage
    localStorage.setItem(STORAGE_KEYS.THEME, theme);
  }, [theme]);

  return <>{children}</>;
}

function App() {
  return (
      <ThemeInitializer>
        <BoardPage/>
      </ThemeInitializer>
  );
}

export default App;